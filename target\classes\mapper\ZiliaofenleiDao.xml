<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.dao.ZiliaofenleiDao">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.entity.ZiliaofenleiEntity" id="ziliaofenleiMap">
        <result property="ziliaofenlei" column="ziliaofenlei"/>
    </resultMap>

	<select id="selectListVO"
		resultType="com.entity.vo.ZiliaofenleiVO" >
		SELECT * FROM ziliaofenlei  ziliaofenlei         
        <where> 1=1 ${ew.sqlSegment}</where>
	</select>
	
	<select id="selectVO"
		resultType="com.entity.vo.ZiliaofenleiVO" >
		SELECT  ziliaofenlei.* FROM ziliaofenlei  ziliaofenlei 	
 		<where> 1=1 ${ew.sqlSegment}</where>
	</select>

    <select id="selectListView"
		resultType="com.entity.view.ZiliaofenleiView" >

		SELECT  ziliaofenlei.* FROM ziliaofenlei  ziliaofenlei 	        
        <where> 1=1 ${ew.sqlSegment}</where>
	</select>
	
	<select id="selectView"
		resultType="com.entity.view.ZiliaofenleiView" >
		SELECT * FROM ziliaofenlei  ziliaofenlei <where> 1=1 ${ew.sqlSegment}</where>
	</select>
	


</mapper>
