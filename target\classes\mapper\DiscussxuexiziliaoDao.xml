<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.dao.DiscussxuexiziliaoDao">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.entity.DiscussxuexiziliaoEntity" id="discussxuexiziliaoMap">
        <result property="refid" column="refid"/>
        <result property="userid" column="userid"/>
        <result property="avatarurl" column="avatarurl"/>
        <result property="nickname" column="nickname"/>
        <result property="content" column="content"/>
        <result property="score" column="score"/>
        <result property="reply" column="reply"/>
        <result property="thumbsupnum" column="thumbsupnum"/>
        <result property="crazilynum" column="crazilynum"/>
        <result property="istop" column="istop"/>
        <result property="tuserids" column="tuserids"/>
        <result property="cuserids" column="cuserids"/>
    </resultMap>

	<select id="selectListVO"
		resultType="com.entity.vo.DiscussxuexiziliaoVO" >
		SELECT * FROM discussxuexiziliao  discussxuexiziliao         
        <where> 1=1 ${ew.sqlSegment}</where>
	</select>
	
	<select id="selectVO"
		resultType="com.entity.vo.DiscussxuexiziliaoVO" >
		SELECT  discussxuexiziliao.* FROM discussxuexiziliao  discussxuexiziliao 	
 		<where> 1=1 ${ew.sqlSegment}</where>
	</select>

    <select id="selectListView"
		resultType="com.entity.view.DiscussxuexiziliaoView" >

		SELECT  discussxuexiziliao.* FROM discussxuexiziliao  discussxuexiziliao 	        
        <where> 1=1 ${ew.sqlSegment}</where>
	</select>
	
	<select id="selectView"
		resultType="com.entity.view.DiscussxuexiziliaoView" >
		SELECT * FROM discussxuexiziliao  discussxuexiziliao <where> 1=1 ${ew.sqlSegment}</where>
	</select>
	


</mapper>
