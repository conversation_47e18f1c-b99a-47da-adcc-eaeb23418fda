<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.dao.XuexiziliaoDao">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.entity.XuexiziliaoEntity" id="xuexiziliaoMap">
        <result property="ziliaomingcheng" column="ziliaomingcheng"/>
        <result property="ziliaofenlei" column="ziliaofenlei"/>
        <result property="fengmian" column="fengmian"/>
        <result property="ziliaowenjian" column="ziliaowenjian"/>
        <result property="ziliaoneirong" column="ziliaoneirong"/>
        <result property="shangchuanshijian" column="shangchuanshijian"/>
        <result property="jiaoshigonghao" column="jiaoshigonghao"/>
        <result property="jiaoshixingming" column="jiaoshixingming"/>
        <result property="sfsh" column="sfsh"/>
        <result property="shhf" column="shhf"/>
        <result property="clicktime" column="clicktime"/>
        <result property="clicknum" column="clicknum"/>
        <result property="discussnum" column="discussnum"/>
        <result property="totalscore" column="totalscore"/>
        <result property="storeupnum" column="storeupnum"/>
    </resultMap>

	<select id="selectListVO"
		resultType="com.entity.vo.XuexiziliaoVO" >
		SELECT * FROM xuexiziliao  xuexiziliao         
        <where> 1=1 ${ew.sqlSegment}</where>
	</select>
	
	<select id="selectVO"
		resultType="com.entity.vo.XuexiziliaoVO" >
		SELECT  xuexiziliao.* FROM xuexiziliao  xuexiziliao 	
 		<where> 1=1 ${ew.sqlSegment}</where>
	</select>

    <select id="selectListView"
		resultType="com.entity.view.XuexiziliaoView" >

		SELECT  xuexiziliao.* FROM xuexiziliao  xuexiziliao 	        
        <where> 1=1 ${ew.sqlSegment}</where>
	</select>
	
	<select id="selectView"
		resultType="com.entity.view.XuexiziliaoView" >
		SELECT * FROM xuexiziliao  xuexiziliao <where> 1=1 ${ew.sqlSegment}</where>
	</select>
	


</mapper>
