<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.dao.TijiaozuoyeDao">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.entity.TijiaozuoyeEntity" id="tijiaozuoyeMap">
        <result property="kechengmingcheng" column="kechengmingcheng"/>
        <result property="kechengleixing" column="kechengleixing"/>
        <result property="fengmian" column="fengmian"/>
        <result property="tijiaozuoye" column="tijiaozuoye"/>
        <result property="pigaizhuangtai" column="pigaizhuangtai"/>
        <result property="tijiaoshijian" column="tijiaoshijian"/>
        <result property="jiaoshigonghao" column="jiaoshigonghao"/>
        <result property="jiaoshixingming" column="jiaoshixingming"/>
        <result property="xuehao" column="xuehao"/>
        <result property="xueshengxingming" column="xueshengxingming"/>
        <result property="crossuserid" column="crossuserid"/>
        <result property="crossrefid" column="crossrefid"/>
    </resultMap>

	<select id="selectListVO"
		resultType="com.entity.vo.TijiaozuoyeVO" >
		SELECT * FROM tijiaozuoye  tijiaozuoye         
        <where> 1=1 ${ew.sqlSegment}</where>
	</select>
	
	<select id="selectVO"
		resultType="com.entity.vo.TijiaozuoyeVO" >
		SELECT  tijiaozuoye.* FROM tijiaozuoye  tijiaozuoye 	
 		<where> 1=1 ${ew.sqlSegment}</where>
	</select>

    <select id="selectListView"
		resultType="com.entity.view.TijiaozuoyeView" >

		SELECT  tijiaozuoye.* FROM tijiaozuoye  tijiaozuoye 	        
        <where> 1=1 ${ew.sqlSegment}</where>
	</select>
	
	<select id="selectView"
		resultType="com.entity.view.TijiaozuoyeView" >
		SELECT * FROM tijiaozuoye  tijiaozuoye <where> 1=1 ${ew.sqlSegment}</where>
	</select>
	


</mapper>
