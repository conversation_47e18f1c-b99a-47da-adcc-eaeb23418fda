<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.dao.XueshengDao">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.entity.XueshengEntity" id="xueshengMap">
        <result property="xuehao" column="xuehao"/>
        <result property="mima" column="mima"/>
        <result property="xueshengxingming" column="xueshengxingming"/>
        <result property="touxiang" column="touxiang"/>
        <result property="xingbie" column="xingbie"/>
        <result property="lianxidianhua" column="lianxidianhua"/>
        <result property="banji" column="banji"/>
    </resultMap>

	<select id="selectListVO"
		resultType="com.entity.vo.XueshengVO" >
		SELECT * FROM xuesheng  xuesheng         
        <where> 1=1 ${ew.sqlSegment}</where>
	</select>
	
	<select id="selectVO"
		resultType="com.entity.vo.XueshengVO" >
		SELECT  xuesheng.* FROM xuesheng  xuesheng 	
 		<where> 1=1 ${ew.sqlSegment}</where>
	</select>

    <select id="selectListView"
		resultType="com.entity.view.XueshengView" >

		SELECT  xuesheng.* FROM xuesheng  xuesheng 	        
        <where> 1=1 ${ew.sqlSegment}</where>
	</select>
	
	<select id="selectView"
		resultType="com.entity.view.XueshengView" >
		SELECT * FROM xuesheng  xuesheng <where> 1=1 ${ew.sqlSegment}</where>
	</select>
	


</mapper>
