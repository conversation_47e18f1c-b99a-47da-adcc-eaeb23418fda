<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.dao.ForumreportDao">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.entity.ForumreportEntity" id="forumreportMap">
        <result property="forumid" column="forumid"/>
        <result property="title" column="title"/>
        <result property="userid" column="userid"/>
        <result property="username" column="username"/>
        <result property="reporteduserid" column="reporteduserid"/>
        <result property="reportedusername" column="reportedusername"/>
        <result property="reason" column="reason"/>
        <result property="picture" column="picture"/>
        <result property="handleadvise" column="handleadvise"/>
        <result property="status" column="status"/>
        <result property="reporttype" column="reporttype"/>
    </resultMap>

	<select id="selectListVO"
		resultType="com.entity.vo.ForumreportVO" >
		SELECT * FROM forumreport  forumreport         
        <where> 1=1 ${ew.sqlSegment}</where>
	</select>
	
	<select id="selectVO"
		resultType="com.entity.vo.ForumreportVO" >
		SELECT  forumreport.* FROM forumreport  forumreport 	
 		<where> 1=1 ${ew.sqlSegment}</where>
	</select>

    <select id="selectListView"
		resultType="com.entity.view.ForumreportView" >

		SELECT  forumreport.* FROM forumreport  forumreport 	        
        <where> 1=1 ${ew.sqlSegment}</where>
	</select>
	
	<select id="selectView"
		resultType="com.entity.view.ForumreportView" >
		SELECT * FROM forumreport  forumreport <where> 1=1 ${ew.sqlSegment}</where>
	</select>
	


</mapper>
