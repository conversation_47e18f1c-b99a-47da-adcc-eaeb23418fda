<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.dao.BanjiDao">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.entity.BanjiEntity" id="banjiMap">
        <result property="banji" column="banji"/>
    </resultMap>

	<select id="selectListVO"
		resultType="com.entity.vo.BanjiVO" >
		SELECT * FROM banji  banji         
        <where> 1=1 ${ew.sqlSegment}</where>
	</select>
	
	<select id="selectVO"
		resultType="com.entity.vo.BanjiVO" >
		SELECT  banji.* FROM banji  banji 	
 		<where> 1=1 ${ew.sqlSegment}</where>
	</select>

    <select id="selectListView"
		resultType="com.entity.view.BanjiView" >

		SELECT  banji.* FROM banji  banji 	        
        <where> 1=1 ${ew.sqlSegment}</where>
	</select>
	
	<select id="selectView"
		resultType="com.entity.view.BanjiView" >
		SELECT * FROM banji  banji <where> 1=1 ${ew.sqlSegment}</where>
	</select>
	


</mapper>
