/**
 * 基于用户的协同过滤推荐算法
 * 
 * 功能说明：
 * 1. 通过计算用户间的余弦相似度来寻找相似用户
 * 2. 基于相似用户的评分历史为目标用户推荐物品
 * 3. 采用加权评分的方式生成推荐列表
 * 
 * <AUTHOR>
 * @version 1.0
 */
public class UserBasedCollaborativeFiltering {
    
    /**
     * 用户-物品评分矩阵
     * 数据结构：userId -> (itemId -> score)
     * 存储所有用户对物品的评分信息
     */
    private final Map<String, Map<String, Double>> userItemRatings;

    /**
     * 构造函数：初始化用户评分矩阵
     * 
     * @param userRatings 用户评分数据
     */
    public UserBasedCollaborativeFiltering(Map<String, Map<String, Double>> userRatings) {
        this.userItemRatings = userRatings;
    }

    /**
     * 计算两个用户之间的余弦相似度
     * 
     * 余弦相似度公式：cos(θ) = (A·B) / (|A|×|B|)
     * 其中：
     * - A·B 表示向量A和B的点积
     * - |A|、|B| 分别表示向量A和B的模长
     * 
     * @param u1 用户1的评分向量
     * @param u2 用户2的评分向量
     * @return 余弦相似度值，范围[0,1]
     */
    private double calculateCosineSimilarity(Map<String, Double> u1, 
                                           Map<String, Double> u2) {
        // 步骤1：找出两个用户共同评分的物品
        Set<String> commonItems = new HashSet<>(u1.keySet());
        commonItems.retainAll(u2.keySet());

        // 步骤2：如果没有共同评分的物品，相似度为0
        if (commonItems.isEmpty()) {
            return 0.0;
        }

        // 步骤3：计算点积 (A·B)
        double dotProduct = 0.0;
        for (String item : commonItems) {
            dotProduct += u1.get(item) * u2.get(item);
        }

        // 步骤4：计算向量的模长 |A| 和 |B|
        double normA = Math.sqrt(u1.values().stream()
                                  .mapToDouble(v -> v * v)
                                  .sum());
        double normB = Math.sqrt(u2.values().stream()
                                  .mapToDouble(v -> v * v)
                                  .sum());

        // 步骤5：返回余弦相似度
        return dotProduct / (normA * normB);
    }

    /**
     * 为指定用户推荐物品
     * 
     * 推荐算法流程：
     * 1. 计算目标用户与其他所有用户的相似度
     * 2. 筛选出相似度大于0的用户作为相似用户
     * 3. 基于相似用户的评分，为目标用户计算未评分物品的推荐分数
     * 4. 按推荐分数降序排列，返回Top-N推荐列表
     * 
     * @param targetUser 目标用户ID
     * @param topN 推荐物品数量
     * @return 推荐物品ID列表，按推荐分数降序排列
     */
    public List<String> recommendItems(String targetUser, int topN) {
        // 获取目标用户的评分记录
        Map<String, Double> targetRatings = userItemRatings.get(targetUser);
        if (targetRatings == null) {
            return Collections.emptyList();
        }

        // 第一步：计算与其他用户的相似度
        Map<String, Double> similarities = new HashMap<>();
        for (Map.Entry<String, Map<String, Double>> entry : userItemRatings.entrySet()) {
            String otherUser = entry.getKey();
            
            // 跳过目标用户自己
            if (otherUser.equals(targetUser)) {
                continue;
            }

            // 计算相似度
            double sim = calculateCosineSimilarity(targetRatings, entry.getValue());
            
            // 只保留相似度大于0的用户
            if (sim > 0) {
                similarities.put(otherUser, sim);
            }
        }

        // 第二步：基于相似用户评分进行加权推荐
        Map<String, Double> scores = new HashMap<>();
        for (Map.Entry<String, Double> simEntry : similarities.entrySet()) {
            String similarUser = simEntry.getKey();
            double similarity = simEntry.getValue();
            Map<String, Double> similarRatings = userItemRatings.get(similarUser);

            // 遍历相似用户的所有评分物品
            for (Map.Entry<String, Double> itemEntry : similarRatings.entrySet()) {
                String item = itemEntry.getKey();

                // 只推荐目标用户未评分的物品
                if (!targetRatings.containsKey(item)) {
                    // 加权计算推荐分数：相似度 × 评分
                    scores.merge(item, similarity * itemEntry.getValue(), Double::sum);
                }
            }
        }

        // 第三步：返回排序后的推荐物品列表
        return scores.entrySet().stream()
                .sorted(Map.Entry.<String, Double>comparingByValue().reversed())
                .limit(topN)
                .map(Map.Entry::getKey)
                .collect(Collectors.toList());
    }
}

/*
 * 使用示例：
 * 
 * // 1. 准备用户评分数据
 * Map<String, Map<String, Double>> ratings = new HashMap<>();
 * 
 * // 2. 创建推荐算法实例
 * UserBasedCollaborativeFiltering cf = new UserBasedCollaborativeFiltering(ratings);
 * 
 * // 3. 为用户"user1"推荐5个物品
 * List<String> recommendations = cf.recommendItems("user1", 5);
 * 
 * // 4. 输出推荐结果
 * System.out.println("推荐物品：" + recommendations);
 */
