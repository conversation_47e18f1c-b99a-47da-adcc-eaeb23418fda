<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.dao.KechengleixingDao">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.entity.KechengleixingEntity" id="kechengleixingMap">
        <result property="kechengleixing" column="kechengleixing"/>
    </resultMap>

	<select id="selectListVO"
		resultType="com.entity.vo.KechengleixingVO" >
		SELECT * FROM kechengleixing  kechengleixing         
        <where> 1=1 ${ew.sqlSegment}</where>
	</select>
	
	<select id="selectVO"
		resultType="com.entity.vo.KechengleixingVO" >
		SELECT  kechengleixing.* FROM kechengleixing  kechengleixing 	
 		<where> 1=1 ${ew.sqlSegment}</where>
	</select>

    <select id="selectListView"
		resultType="com.entity.view.KechengleixingView" >

		SELECT  kechengleixing.* FROM kechengleixing  kechengleixing 	        
        <where> 1=1 ${ew.sqlSegment}</where>
	</select>
	
	<select id="selectView"
		resultType="com.entity.view.KechengleixingView" >
		SELECT * FROM kechengleixing  kechengleixing <where> 1=1 ${ew.sqlSegment}</where>
	</select>
	


</mapper>
