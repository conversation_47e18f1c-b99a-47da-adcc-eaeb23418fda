<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.dao.ZuoyexinxiDao">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.entity.ZuoyexinxiEntity" id="zuoyexinxiMap">
        <result property="kechengmingcheng" column="kechengmingcheng"/>
        <result property="kechengleixing" column="kechengleixing"/>
        <result property="fengmian" column="fengmian"/>
        <result property="zuoyeneirong" column="zuoyeneirong"/>
        <result property="banji" column="banji"/>
        <result property="fabushijian" column="fabushijian"/>
        <result property="jiaoshigonghao" column="jiaoshigonghao"/>
        <result property="jiaoshixingming" column="jiaoshixingming"/>
        <result property="reversetime" column="reversetime"/>
    </resultMap>

	<select id="selectListVO"
		resultType="com.entity.vo.ZuoyexinxiVO" >
		SELECT * FROM zuoyexinxi  zuoyexinxi         
        <where> 1=1 ${ew.sqlSegment}</where>
	</select>
	
	<select id="selectVO"
		resultType="com.entity.vo.ZuoyexinxiVO" >
		SELECT  zuoyexinxi.* FROM zuoyexinxi  zuoyexinxi 	
 		<where> 1=1 ${ew.sqlSegment}</where>
	</select>

    <select id="selectListView"
		resultType="com.entity.view.ZuoyexinxiView" >

		SELECT  zuoyexinxi.* FROM zuoyexinxi  zuoyexinxi 	        
        <where> 1=1 ${ew.sqlSegment}</where>
	</select>
	
	<select id="selectView"
		resultType="com.entity.view.ZuoyexinxiView" >
		SELECT * FROM zuoyexinxi  zuoyexinxi <where> 1=1 ${ew.sqlSegment}</where>
	</select>
	


</mapper>
