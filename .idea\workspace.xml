<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="SELECTIVE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="405b5e67-37a7-477b-8717-233605c4ceeb" name="Default Changelist" comment="Default Changelist">
      <change afterPath="$PROJECT_DIR$/.editorconfig" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/main/resources/admin/admin/src/assets/img/login-bg.jpg" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/.idea/encodings.xml" beforeDir="false" afterPath="$PROJECT_DIR$/.idea/encodings.xml" afterDir="false" />
    </list>
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="Git.Settings">
    <option name="RECENT_GIT_ROOT_PATH" value="$PROJECT_DIR$" />
  </component>
  <component name="HierarchyBrowserManager">
    <option name="SCOPE" value="All" />
  </component>
  <component name="HighlightingSettingsPerFile">
    <setting file="file://$PROJECT_DIR$/src/main/java/com/controller/BanjiController.java" root0="SKIP_INSPECTION" />
    <setting file="file://$PROJECT_DIR$/src/main/java/com/controller/CommonController.java" root0="SKIP_INSPECTION" />
    <setting file="file://$PROJECT_DIR$/src/main/java/com/controller/DiscusskechengxinxiController.java" root0="SKIP_INSPECTION" />
    <setting file="file://$PROJECT_DIR$/src/main/java/com/controller/DiscussxuexiziliaoController.java" root0="SKIP_INSPECTION" />
    <setting file="file://$PROJECT_DIR$/src/main/java/com/controller/ExamquestionController.java" root0="SKIP_INSPECTION" />
    <setting file="file://$PROJECT_DIR$/src/main/java/com/controller/ExamquestionbankController.java" root0="SKIP_INSPECTION" />
    <setting file="file://$PROJECT_DIR$/src/main/java/com/controller/ExamrecordController.java" root0="SKIP_INSPECTION" />
    <setting file="file://$PROJECT_DIR$/src/main/java/com/controller/FileController.java" root0="SKIP_INSPECTION" />
    <setting file="file://$PROJECT_DIR$/src/main/java/com/controller/ForumreportController.java" root0="SKIP_INSPECTION" />
    <setting file="file://$PROJECT_DIR$/src/main/java/com/controller/ForumtypeController.java" root0="SKIP_INSPECTION" />
    <setting file="file://$PROJECT_DIR$/src/main/java/com/controller/JiaoshiController.java" root0="SKIP_INSPECTION" />
    <setting file="file://$PROJECT_DIR$/src/main/java/com/controller/KechengleixingController.java" root0="SKIP_INSPECTION" />
    <setting file="file://$PROJECT_DIR$/src/main/java/com/controller/KechengxinxiController.java" root0="SKIP_INSPECTION" />
    <setting file="file://$PROJECT_DIR$/src/main/java/com/controller/NewsController.java" root0="SKIP_INSPECTION" />
    <setting file="file://$PROJECT_DIR$/src/main/java/com/controller/NewstypeController.java" root0="SKIP_INSPECTION" />
    <setting file="file://$PROJECT_DIR$/src/main/java/com/controller/StoreupController.java" root0="SKIP_INSPECTION" />
    <setting file="file://$PROJECT_DIR$/src/main/java/com/controller/TijiaozuoyeController.java" root0="SKIP_INSPECTION" />
    <setting file="file://$PROJECT_DIR$/src/main/java/com/controller/XiangmuleixingController.java" root0="SKIP_INSPECTION" />
    <setting file="file://$PROJECT_DIR$/src/main/java/com/controller/XueshengController.java" root0="SKIP_INSPECTION" />
    <setting file="file://$PROJECT_DIR$/src/main/java/com/controller/XuexiziliaoController.java" root0="SKIP_INSPECTION" />
    <setting file="file://$PROJECT_DIR$/src/main/java/com/controller/ZiliaofenleiController.java" root0="SKIP_INSPECTION" />
    <setting file="file://$PROJECT_DIR$/src/main/java/com/controller/ZuoyechengjiController.java" root0="SKIP_INSPECTION" />
    <setting file="file://$PROJECT_DIR$/src/main/java/com/controller/ZuoyexinxiController.java" root0="SKIP_INSPECTION" />
    <setting file="file://$PROJECT_DIR$/src/main/resources/admin/admin/src/views/home.vue" root0="SKIP_INSPECTION" />
    <setting file="file://$PROJECT_DIR$/src/main/resources/admin/admin/src/views/login.vue" root0="SKIP_INSPECTION" />
    <setting file="file://$PROJECT_DIR$/src/main/resources/admin/admin/src/views/register.vue" root0="SKIP_INSPECTION" />
  </component>
  <component name="MavenImportPreferences">
    <option name="generalSettings">
      <MavenGeneralSettings>
        <option name="customMavenHome" value="$PROJECT_DIR$/../../package/apache-maven-3.5.0" />
        <option name="mavenHomeTypeForPersistence" value="CUSTOM" />
        <option name="userSettingsFile" value="D:\package\apache-maven-3.5.0\conf\settings.xml" />
      </MavenGeneralSettings>
    </option>
  </component>
  <component name="ProjectColorInfo">{
  &quot;customColor&quot;: &quot;&quot;,
  &quot;associatedIndex&quot;: 0
}</component>
  <component name="ProjectId" id="2x8I30gfVu4tueJK1p4R6pi4ybq" />
  <component name="ProjectLevelVcsManager" settingsEditedManually="true" />
  <component name="ProjectViewState">
    <option name="openDirectoriesWithSingleClick" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent">{
  &quot;keyToString&quot;: {
    &quot;ASKED_SHARE_PROJECT_CONFIGURATION_FILES&quot;: &quot;true&quot;,
    &quot;Notification.DisplayName-DoNotAsk-Database view&quot;: &quot;数据库&quot;,
    &quot;Notification.DoNotAsk-Database view&quot;: &quot;true&quot;,
    &quot;RequestMappingsPanelOrder0&quot;: &quot;0&quot;,
    &quot;RequestMappingsPanelOrder1&quot;: &quot;1&quot;,
    &quot;RequestMappingsPanelWidth0&quot;: &quot;75&quot;,
    &quot;RequestMappingsPanelWidth1&quot;: &quot;75&quot;,
    &quot;RunOnceActivity.ShowReadmeOnStart&quot;: &quot;true&quot;,
    &quot;Spring Boot.SpringbootSchemaApplication.executor&quot;: &quot;Run&quot;,
    &quot;kotlin-language-version-configured&quot;: &quot;true&quot;,
    &quot;last_opened_file_path&quot;: &quot;D:/JavaProjects/learning/src/main/resources/admin/admin/src/assets/img&quot;,
    &quot;node.js.detected.package.eslint&quot;: &quot;true&quot;,
    &quot;node.js.detected.package.tslint&quot;: &quot;true&quot;,
    &quot;node.js.selected.package.eslint&quot;: &quot;(autodetect)&quot;,
    &quot;node.js.selected.package.tslint&quot;: &quot;(autodetect)&quot;,
    &quot;nodejs_package_manager_path&quot;: &quot;npm&quot;,
    &quot;run.code.analysis.last.selected.profile&quot;: &quot;pProject Default&quot;,
    &quot;settings.editor.selected.configurable&quot;: &quot;File.Encoding&quot;,
    &quot;ts.external.directory.path&quot;: &quot;D:\\PERSONAL\\IDEA\\IntelliJ IDEA 2024.1\\plugins\\javascript-impl\\jsLanguageServicesImpl\\external&quot;,
    &quot;vue.rearranger.settings.migration&quot;: &quot;true&quot;
  }
}</component>
  <component name="RecentsManager">
    <key name="CopyFile.RECENT_KEYS">
      <recent name="D:\JavaProjects\learning\src\main\resources\admin\admin\src\assets\img" />
    </key>
  </component>
  <component name="RunManager">
    <configuration name="SpringbootSchemaApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot">
      <module name="learning" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="com.SpringbootSchemaApplication" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
  </component>
  <component name="SharedIndexes">
    <attachedChunks>
      <set>
        <option value="bundled-jdk-9f38398b9061-18abd8497189-intellij.indexing.shared.core-IU-241.14494.240" />
        <option value="bundled-js-predefined-1d06a55b98c1-74d2a5396914-JavaScript-IU-241.14494.240" />
      </set>
    </attachedChunks>
  </component>
  <component name="SpellCheckerSettings" RuntimeDictionaries="0" Folders="0" CustomDictionaries="0" DefaultDictionary="应用程序级" UseSingleDictionary="true" transferred="true" />
  <component name="SvnConfiguration">
    <configuration />
  </component>
  <component name="TaskManager">
    <task active="true" id="Default" summary="Default task">
      <changelist id="405b5e67-37a7-477b-8717-233605c4ceeb" name="Default Changelist" comment="" />
      <created>1747313070376</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1747313070376</updated>
      <workItem from="1747313071684" duration="1224000" />
      <workItem from="1747718038426" duration="558000" />
      <workItem from="1747720225440" duration="484000" />
      <workItem from="1747720721423" duration="6453000" />
      <workItem from="1747731322135" duration="1415000" />
      <workItem from="1747738799790" duration="4276000" />
      <workItem from="1747805632922" duration="408000" />
      <workItem from="1747806059504" duration="8211000" />
      <workItem from="1747831601189" duration="3176000" />
      <workItem from="1747834876581" duration="2256000" />
      <workItem from="1747838367509" duration="38000" />
      <workItem from="1747838412844" duration="1213000" />
      <workItem from="1747872887625" duration="2149000" />
      <workItem from="1747877416302" duration="570000" />
      <workItem from="1747878025400" duration="145000" />
      <workItem from="1747878187023" duration="642000" />
      <workItem from="1747879013707" duration="41000" />
      <workItem from="1747879073052" duration="2526000" />
      <workItem from="1747881768794" duration="1929000" />
      <workItem from="1747884049852" duration="973000" />
      <workItem from="1747885041208" duration="733000" />
      <workItem from="1747886182291" duration="198000" />
      <workItem from="1747886391963" duration="293000" />
      <workItem from="1747886693848" duration="3925000" />
      <workItem from="1747893129826" duration="181000" />
      <workItem from="1747898300478" duration="138000" />
      <workItem from="1747898807108" duration="584000" />
      <workItem from="1747899404956" duration="54000" />
      <workItem from="1747899499588" duration="677000" />
      <workItem from="1747900197523" duration="1180000" />
      <workItem from="1747901455355" duration="2895000" />
      <workItem from="1747904812176" duration="1860000" />
      <workItem from="1747907468851" duration="595000" />
      <workItem from="1747908095111" duration="1551000" />
      <workItem from="1747909852400" duration="174000" />
      <workItem from="1747910044960" duration="1310000" />
      <workItem from="1747911382187" duration="653000" />
      <workItem from="1747912078582" duration="454000" />
      <workItem from="1747912758530" duration="318000" />
      <workItem from="1747913139047" duration="69000" />
      <workItem from="1747913219130" duration="162000" />
      <workItem from="1747913413358" duration="113000" />
      <workItem from="1747913539268" duration="464000" />
      <workItem from="1747914017575" duration="293000" />
      <workItem from="1747914444335" duration="99000" />
      <workItem from="1747914547785" duration="382000" />
      <workItem from="1747915073918" duration="154000" />
      <workItem from="1747915317253" duration="1241000" />
      <workItem from="1747916580132" duration="1291000" />
      <workItem from="1747919000273" duration="296000" />
      <workItem from="1747919314076" duration="220000" />
      <workItem from="1747919548970" duration="533000" />
      <workItem from="1747920206941" duration="30000" />
      <workItem from="1747920336435" duration="238000" />
      <workItem from="1747920587823" duration="166000" />
      <workItem from="1747920766218" duration="831000" />
      <workItem from="1747921615528" duration="377000" />
      <workItem from="1747922006457" duration="104000" />
      <workItem from="1747922123065" duration="285000" />
      <workItem from="1747922422510" duration="491000" />
      <workItem from="1747924819642" duration="541000" />
      <workItem from="1747925589619" duration="260000" />
      <workItem from="1747925861107" duration="812000" />
      <workItem from="1747926696155" duration="875000" />
      <workItem from="1747930191155" duration="3725000" />
      <workItem from="1748039074605" duration="322000" />
      <workItem from="1748039613565" duration="8000" />
      <workItem from="1748039656413" duration="5077000" />
      <workItem from="1748049804263" duration="2587000" />
      <workItem from="1748055088685" duration="125000" />
      <workItem from="1748055410823" duration="552000" />
      <workItem from="1748055984771" duration="610000" />
      <workItem from="1748058344019" duration="608000" />
      <workItem from="1748073397250" duration="84000" />
      <workItem from="1748090877076" duration="2526000" />
      <workItem from="1748165680728" duration="6949000" />
      <workItem from="1748868199682" duration="1022000" />
      <workItem from="1748935038358" duration="1700000" />
      <workItem from="1748949060163" duration="43000" />
      <workItem from="1749102537536" duration="14000" />
      <workItem from="1749102705940" duration="89000" />
      <workItem from="1749563808782" duration="322000" />
      <workItem from="1749627382120" duration="671000" />
    </task>
    <task id="LOCAL-00001" summary="Default Changelist">
      <option name="closed" value="true" />
      <created>1747731286446</created>
      <option name="number" value="00001" />
      <option name="presentableId" value="LOCAL-00001" />
      <option name="project" value="LOCAL" />
      <updated>1747731286446</updated>
    </task>
    <task id="LOCAL-00002" summary="Default Changelist">
      <option name="closed" value="true" />
      <created>1747731556978</created>
      <option name="number" value="00002" />
      <option name="presentableId" value="LOCAL-00002" />
      <option name="project" value="LOCAL" />
      <updated>1747731556978</updated>
    </task>
    <task id="LOCAL-00003" summary="Default Changelist">
      <option name="closed" value="true" />
      <created>1747898432006</created>
      <option name="number" value="00003" />
      <option name="presentableId" value="LOCAL-00003" />
      <option name="project" value="LOCAL" />
      <updated>1747898432006</updated>
    </task>
    <option name="localTasksCounter" value="4" />
    <servers />
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="3" />
  </component>
  <component name="Vcs.Log.Tabs.Properties">
    <option name="TAB_STATES">
      <map>
        <entry key="MAIN">
          <value>
            <State />
          </value>
        </entry>
      </map>
    </option>
  </component>
  <component name="VcsManagerConfiguration">
    <MESSAGE value="Default Changelist" />
    <option name="LAST_COMMIT_MESSAGE" value="Default Changelist" />
  </component>
  <component name="XSLT-Support.FileAssociations.UIState">
    <expand />
    <select />
  </component>
</project>