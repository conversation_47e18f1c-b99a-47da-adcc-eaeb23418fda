<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.dao.ZuoyechengjiDao">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.entity.ZuoyechengjiEntity" id="zuoyechengjiMap">
        <result property="kechengmingcheng" column="kechengmingcheng"/>
        <result property="kechengleixing" column="kechengleixing"/>
        <result property="fengmian" column="fengmian"/>
        <result property="chengji" column="chengji"/>
        <result property="pingyu" column="pingyu"/>
        <result property="pigaishijian" column="pigaishijian"/>
        <result property="jiaoshigonghao" column="jiaoshigonghao"/>
        <result property="jiaoshixingming" column="jiaoshixingming"/>
        <result property="xuehao" column="xuehao"/>
        <result property="xueshengxingming" column="xueshengxingming"/>
    </resultMap>

	<select id="selectListVO"
		resultType="com.entity.vo.ZuoyechengjiVO" >
		SELECT * FROM zuoyechengji  zuoyechengji         
        <where> 1=1 ${ew.sqlSegment}</where>
	</select>
	
	<select id="selectVO"
		resultType="com.entity.vo.ZuoyechengjiVO" >
		SELECT  zuoyechengji.* FROM zuoyechengji  zuoyechengji 	
 		<where> 1=1 ${ew.sqlSegment}</where>
	</select>

    <select id="selectListView"
		resultType="com.entity.view.ZuoyechengjiView" >

		SELECT  zuoyechengji.* FROM zuoyechengji  zuoyechengji 	        
        <where> 1=1 ${ew.sqlSegment}</where>
	</select>
	
	<select id="selectView"
		resultType="com.entity.view.ZuoyechengjiView" >
		SELECT * FROM zuoyechengji  zuoyechengji <where> 1=1 ${ew.sqlSegment}</where>
	</select>
	

    <select id="selectValue" resultType="map" >
        SELECT ${params.xColumn}, ROUND(sum(${params.yColumn}),1) total FROM zuoyechengji
        <where> 1=1 ${ew.sqlSegment}</where>
        group by ${params.xColumn}
    </select>

    <select id="selectTimeStatValue" resultType="map" >
        <if test = 'params.timeStatType == "日"'>
                SELECT DATE_FORMAT(${params.xColumn},'%Y-%m-%d') ${params.xColumn}, sum(${params.yColumn}) total FROM zuoyechengji
                <where> 1=1 ${ew.sqlSegment}</where>
                group by DATE_FORMAT(${params.xColumn},'%Y-%m-%d')
        </if>
        <if test = 'params.timeStatType == "月"'>
                SELECT DATE_FORMAT(${params.xColumn},'%Y-%m') ${params.xColumn}, sum(${params.yColumn}) total FROM zuoyechengji
                <where> 1=1 ${ew.sqlSegment}</where>
                group by DATE_FORMAT(${params.xColumn},'%Y-%m')
        </if>
        <if test='params.timeStatType == "季"'>
                SELECT CONCAT(YEAR(${params.xColumn}), ' - ', ELT(QUARTER(${params.xColumn}), '第一季度', '第二季度', '第三季度', '第四季度')) as ${params.xColumn}, sum(${params.yColumn}) total FROM orders
                <where> 1=1 ${ew.sqlSegment}</where>
                group by CONCAT(YEAR(${params.xColumn}), ' - ', ELT(QUARTER(${params.xColumn}), '第一季度', '第二季度', '第三季度', '第四季度'))
        </if>
        <if test = 'params.timeStatType == "年"'>
                SELECT DATE_FORMAT(${params.xColumn},'%Y') ${params.xColumn}, sum(${params.yColumn}) total FROM zuoyechengji
                <where> 1=1 ${ew.sqlSegment}</where>
                group by DATE_FORMAT(${params.xColumn},'%Y')
        </if>
    </select>

    <select id="selectGroup" resultType="map" >
        SELECT ${params.column} , count(1) total FROM zuoyechengji
        <where> 1=1 ${ew.sqlSegment}</where>
        group by ${params.column}
    </select>




</mapper>
