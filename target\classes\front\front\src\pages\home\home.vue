<template>
	<div class="home-preview">




		<!-- 新闻资讯 -->
		<div id="animate_newsnews" class="news animate__animated">
			<div class="news_title_box">
				<span class="news_title">公告信息</span>
				<span class="news_subhead">{{'news'.toUpperCase()}}</span>
			</div>
			<div class="list list15 index-pv1">
				<div class="tab">
					<div class="item" :class="newsIndex15 == 0 ? 'active' : ''" @click="newsIndexClick15(0)">
						<div>全部</div>
					</div>
					<template v-for="(item,index) in newsCategoryList15">
						<div v-if="index < 6" class="item" :class="newsIndex15 == index + 1 ? 'active' : ''" @click="newsIndexClick15(index+1)">
							<div>{{item.typename}}</div>
						</div>
					</template>
					<div class="more" @click="moreBtn('news')">
						<div>更多</div>
						<span class="icon iconfont icon-jiantou25"></span>
					</div>
				</div>
				<div v-if="newsList.length" class="content">
					<div @click="toDetail('newsDetail', newsList[0])" class="left list-item animation-box">
						<img :src="baseUrl + newsList[0].picture">
						<div class="name">{{newsList[0].title}}</div>
						<div class="desc">{{newsList[0].introduction}}</div>
						<div class="time_item">
							<span class="icon iconfont icon-shijian21"></span>
							<span class="label">发布时间：</span>
							<span class="text">{{newsList[0].addtime.split(' ')[0]}}</span>
						</div>
						<div class="publisher_item">
							<span class="icon iconfont icon-geren16"></span>
							<span class="label">发布人：</span>
							<span class="text">{{newsList[0].name}}</span>
						</div>
						<div class="like_item">
							<span class="icon iconfont icon-zan10"></span>
							<span class="label">点赞：</span>
							<span class="text">{{newsList[0].thumbsupnum}}</span>
						</div>
						<div class="collect_item">
							<span class="icon iconfont icon-shoucang10"></span>
							<span class="label">收藏：</span>
							<span class="text">{{newsList[0].storeupnum}}</span>
						</div>
						<div class="view_item">
							<span class="icon iconfont icon-chakan2"></span>
							<span class="label">浏览次数：</span>
							<span class="text">{{newsList[0].clicknum}}</span>
						</div>
					</div>
					<div v-if="newsList.length > 1" class="right">
						<template v-for="(item,index) in newsList">
							<div v-if="index > 0" @click="toDetail('newsDetail', item)" class="list-item animation-box">
								<div class="date">
									<div class="day">{{item.addtime.split(" ")[0].split("-")[2]}}</div>
									<div class="year">{{item.addtime.split(" ")[0].split("-")[0] + '-' + item.addtime.split(" ")[0].split("-")[1]}}</div>
								</div>
								<div class="info">
									<div class="name">{{item.title}}</div>
									<div class="desc">{{item.introduction}}</div>
									<div class="publisher">
										<span class="icon iconfont icon-geren16"></span>
										<span class="label">发布人：</span>
										<span class="text">{{item.name}}</span>
									</div>
									<div class="like">
										<span class="icon iconfont icon-zan10"></span>
										<span class="label">点赞：</span>
										<span class="text">{{item.thumbsupnum}}</span>
									</div>
									<div class="collect">
										<span class="icon iconfont icon-shoucang10"></span>
										<span class="label">收藏：</span>
										<span class="text">{{item.storeupnum}}</span>
									</div>
									<div class="view">
										<span class="icon iconfont icon-chakan2"></span>
										<span class="label">点击：</span>
										<span class="text">{{item.clicknum}}</span>
									</div>
								</div>
							</div>
						</template>
					</div>
				</div>
			</div>
			<div class="moreBtn" @click="moreBtn('news')">
				<span class="text">MORE+</span>
				<i class="icon iconfont icon-gengduo1"></i>
			</div>
		</div>
		<!-- 新闻资讯 -->
		<!-- 商品推荐 -->
		<div id="animate_recommendxuexiziliao" class="recommend animate__animated">
			<div class="recommend_title_box">
				<span class="recommend_title">学习资料推荐</span>
				<span class="recommend_subhead">{{'xuexiziliao'.toUpperCase()}} RECOMMEND</span>
			</div>
			<div v-if="xuexiziliaoRecommend.length" class="list list16 index-pv1">
				<div v-for="item,index in xuexiziliaoRecommend" :key="index"  @click="toDetail('xuexiziliaoDetail', item)" class="list-item animation-box">
					<div class="img">
						<img v-if="preHttp(item.fengmian)&&preHttp2(item.fengmian)" :src="item.fengmian" alt="" />
						<img v-else-if="preHttp(item.fengmian)" :src="item.fengmian.split(',')[0]" alt="" />
						<img v-else :src="baseUrl + (item.fengmian?item.fengmian.split(',')[0]:'')" alt="" />
					</div>
					<div class="infoBox">
						<div class="info-left">
							<div class="name">资料名称:{{item.ziliaomingcheng}}</div>
							<div class="name">{{item.ziliaofenlei}}</div>
							<div class="time_item">
								<span class="icon iconfont icon-shijian21"></span>
								<span class="label">发布时间：</span>
								<span class="text">{{item.addtime.split(' ')[0]}}</span>
							</div>
							<div class="publisher_item">
								<span class="icon iconfont icon-geren16"></span>
								<span class="label">发布人：</span>
								<span class="text">{{item.jiaoshigonghao}}</span>
							</div>
							<div class="collect_item">
								<span class="icon iconfont icon-shoucang10"></span>
								<span class="label">收藏：</span>
								<span class="text">{{item.storeupnum}}</span>
							</div>
							<div class="view_item">
								<span class="icon iconfont icon-chakan9"></span>
								<span class="label">浏览次数：</span>
								<span class="text">{{item.clicknum}}</span>
							</div>
						</div>
						<div class="desc ql-snow ql-editor" v-html="item.ziliaoneirong"></div>
					</div>
				</div>
			</div>
			<div class="moreBtn" @click="moreBtn('xuexiziliao')">
				<span class="text">MORE+</span>
				<i class="icon iconfont icon-gengduo1"></i>
			</div>
		</div>
		<!-- 商品推荐 -->
		<!-- 商品推荐 -->
		<div id="animate_recommendkechengxinxi" class="recommend animate__animated">
			<div class="recommend_title_box">
				<span class="recommend_title">课程信息推荐</span>
				<span class="recommend_subhead">{{'kechengxinxi'.toUpperCase()}} RECOMMEND</span>
			</div>
			<div v-if="kechengxinxiRecommend.length" class="list list16 index-pv1">
				<div v-for="item,index in kechengxinxiRecommend" :key="index"  @click="toDetail('kechengxinxiDetail', item)" class="list-item animation-box">
					<div class="img">
						<img v-if="preHttp(item.fengmian)&&preHttp2(item.fengmian)" :src="item.fengmian" alt="" />
						<img v-else-if="preHttp(item.fengmian)" :src="item.fengmian.split(',')[0]" alt="" />
						<img v-else :src="baseUrl + (item.fengmian?item.fengmian.split(',')[0]:'')" alt="" />
					</div>
					<div class="infoBox">
						<div class="info-left">
							<div class="name">课程名称:{{item.kechengmingcheng}}</div>
							<div class="name">{{item.kechengleixing}}</div>
							<div class="time_item">
								<span class="icon iconfont icon-shijian21"></span>
								<span class="label">发布时间：</span>
								<span class="text">{{item.addtime.split(' ')[0]}}</span>
							</div>
							<div class="publisher_item">
								<span class="icon iconfont icon-geren16"></span>
								<span class="label">发布人：</span>
								<span class="text">{{item.jiaoshigonghao}}</span>
							</div>
							<div class="collect_item">
								<span class="icon iconfont icon-shoucang10"></span>
								<span class="label">收藏：</span>
								<span class="text">{{item.storeupnum}}</span>
							</div>
							<div class="view_item">
								<span class="icon iconfont icon-chakan9"></span>
								<span class="label">浏览次数：</span>
								<span class="text">{{item.clicknum}}</span>
							</div>
						</div>
						<div class="desc ql-snow ql-editor" v-html="item.dagang"></div>
					</div>
				</div>
			</div>
			<div class="moreBtn" @click="moreBtn('kechengxinxi')">
				<span class="text">MORE+</span>
				<i class="icon iconfont icon-gengduo1"></i>
			</div>
		</div>
		<!-- 商品推荐 -->

	</div>
</template>

<script>
import 'animate.css'
import Swiper from "swiper";

	export default {
		//数据集合
		data() {
			return {
				baseUrl: '',
				newsList: [],
				xuexiziliaoRecommend: [],
				kechengxinxiRecommend: [],

			

				newsIndex15: 0,
				newsCategoryList15: [],



			}
		},
		created() {
			this.baseUrl = this.$config.baseUrl;
			this.getNewsList();
			this.getNewsCategory()
			this.getList();
		},
		mounted() {
			window.addEventListener('scroll', this.handleScroll)
			setTimeout(()=>{
				this.handleScroll()
			},100)
			
			this.swiperChanges()
		},
		beforeDestroy() {
			window.removeEventListener('scroll', this.handleScroll)
		},
		//方法集合
		methods: {
			swiperChanges() {
				setTimeout(()=>{
				},750)
			},
			newsIndexClick15(index) {
				this.newsIndex15 = index
				this.getNewsList()
			},
			getNewsCategory() {
				this.$http.get('newstype/list', {}).then(res => {
					if (res.data.code == 0) {
						this.newsCategoryList15 = res.data.data
					}
				});
			},


			handleScroll() {
				let arr = [
					{id:'about',css:'animate__'},
					{id:'system',css:'animate__'},
					{id:'animate_recommendxuexiziliao',css:'animate__'},
					{id:'animate_recommendkechengxinxi',css:'animate__'},
				
					{id:'animate_newsnews',css:'animate__'},
				]
			
				for (let i in arr) {
					let doc = document.getElementById(arr[i].id)
					if (doc) {
						let top = doc.offsetTop
						let win_top = window.innerHeight + window.pageYOffset
						// console.log(top,win_top)
						if (win_top > top && doc.classList.value.indexOf(arr[i].css) < 0) {
							// console.log(doc)
							doc.classList.add(arr[i].css)
						}
					}
				}
			},
			preHttp(str) {
				return str && str.substr(0,4)=='http';
			},
			preHttp2(str) {
				return str && str.split(',w').length>1;
			},
			getNewsList() {
				let data = {
					page: 1,
					limit: 7,
					sort: 'addtime',
					order: 'desc'
				}
				if(this.newsIndex15!=0) {
					data['typename'] = this.newsCategoryList15[this.newsIndex15 - 1].typename
				}
				this.$http.get('news/list', {params: data}).then(res => {
					if (res.data.code == 0) {
						this.newsList = res.data.data.list;
					
					}
				});
			},
			getList() {
				let autoSortUrl = "";
				let data = {}
				autoSortUrl = "xuexiziliao/autoSort";
				data = {
					page: 1,
					limit: 4,
				}
				data['sfsh'] = '是'
				this.$http.get(autoSortUrl, {params: data}).then(res => {
					if (res.data.code == 0) {
						this.xuexiziliaoRecommend = res.data.data.list;
					}
				});
				autoSortUrl = "kechengxinxi/autoSort";
				if(localStorage.getItem('frontToken')) {
					autoSortUrl = "kechengxinxi/autoSort2";
				}
				data = {
					page: 1,
					limit: 4,
				}
				data['sfsh'] = '是'
				this.$http.get(autoSortUrl, {params: data}).then(res => {
					if (res.data.code == 0) {
						this.kechengxinxiRecommend = res.data.data.list;
					}
				});
			
				
			},
			toDetail(path, item) {
				this.$router.push({path: '/index/' + path, query: {id: item.id}});
			},
			moreBtn(path) {
				this.$router.push({path: '/index/' + path});
			}
		}
	}
</script>

<style rel="stylesheet/scss" lang="scss" scoped>
	.home-preview {
		margin: 0px auto;
		flex-direction: column;
		background: #fff;
		display: flex;
		width: 100%;
		.news {
			padding: 0;
			margin: 0;
			background: #fff;
			width: 100%;
			position: relative;
			order: 1;
			.news_title_box {
				padding: 0 0 0 40px;
				margin: 0 auto 10px;
				background: url(http://codegen.caihongy.cn/20250208/d060099b89474d108926a6ffdc2a8b7d.png) no-repeat left center / 34px;
				width: 1200px;
				position: relative;
				text-align: left;
				.news_title {
					margin: 0;
					color: #fa710d;
					background: none;
					width: auto;
					font-size: 22px;
					line-height: 40px;
					text-align: left;
				}
				.news_subhead {
					margin: 0;
					color: #999;
					display: none;
					width: auto;
					font-size: 18px;
					line-height: 40px;
					text-align: center;
				}
			}
			.index-pv1 .animation-box:hover {
				transform: rotate(0deg) scale(1) skew(0deg, 0deg) translate3d(0px, 0px, 0px);
				-webkit-perspective: 1000px;
				perspective: 1000px;
				transition: 0.3s;
				z-index: 1;
			}
			.index-pv1 .animation-box img:hover {
				transform: rotate(0deg) scale(1) skew(0deg, 0deg) translate3d(0px, 0px, 0px);
				-webkit-perspective: 1000px;
				perspective: 1000px;
				transition: 0.3s;
			}
			.list15 {
				padding: 0;
				margin: 0px auto;
				color: #888;
				background: #fff;
				width: 1200px;
				font-size: 14px;
				height: auto;
				.tab {
					margin: 0 0 20px;
					display: none;
					width: 100%;
					justify-content: center;
					height: auto;
					.item {
						cursor: pointer;
						border: 1px solid #fff;
						border-radius: 2px;
						padding: 5px 10px;
						margin: 0 10px;
						color: #333;
						background: #fff;
						display: flex;
						line-height: 44px;
						align-items: center;
					}
					
					.item:hover {
						border: 1px solid #ddd;
						color: #333;
						background: #fff;
					}
					
					.item.active {
						border: 1px solid #ddd;
						color: #333;
						background: #fff;
					}
					.more {
						cursor: pointer;
						border-radius: 6px;
						padding: 5px 10px;
						margin: 0 10px;
						color: #fff;
						background: #333;
						display: none;
						line-height: 44px;
						align-items: center;
					}
					
					.more:hover {
						background: red;
					}
				}
				.content {
					display: flex;
					width: 100%;
					justify-content: space-between;
					height: auto;
					.left.list-item {
						cursor: pointer;
						padding: 0 0 10px;
						margin: 10px 0;
						background: #fff;
						width: 32%;
						border-color: #ffa200 #efefef #eee #efefef;
						border-width: 10px 1px 1px 1px;
						border-style: solid;
						height: auto;
						img {
							object-fit: cover;
							display: block;
							width: 100%;
							height: 250px;
						}
						.name {
							padding: 0 10px;
							overflow: hidden;
							color: #333;
							white-space: nowrap;
							font-weight: 600;
							width: 100%;
							font-size: 15px;
							line-height: 30px;
							text-overflow: ellipsis;
						}
						.desc {
							padding: 0 10px;
							overflow: hidden;
							color: #666;
							font-size: 14px;
							line-height: 24px;
							height: 48px;
						}
						.time_item {
							padding: 0 10px;
							display: inline-block;
							.icon {
								margin: 0 2px 0 0;
								line-height: 1.5;
							}
							.label {
								line-height: 1.5;
							}
							.text {
								line-height: 1.5;
							}
						}
						.publisher_item {
							padding: 0 10px;
							display: inline-block;
							.icon {
								margin: 0 2px 0 0;
								line-height: 1.5;
							}
							.label {
								line-height: 1.5;
							}
							.text {
								line-height: 1.5;
							}
						}
						.like_item {
							padding: 0 10px;
							display: inline-block;
							.icon {
								margin: 0 2px 0 0;
								line-height: 1.5;
							}
							.label {
								line-height: 1.5;
							}
							.text {
								line-height: 1.5;
							}
						}
						.collect_item {
							padding: 0 10px;
							display: inline-block;
							.icon {
								margin: 0 2px 0 0;
								line-height: 1.5;
							}
							.label {
								line-height: 1.5;
							}
							.text {
								line-height: 1.5;
							}
						}
						.view_item {
							padding: 0 10px;
							display: inline-block;
							.icon {
								margin: 0 2px 0 0;
								line-height: 1.5;
							}
							.label {
								line-height: 1.5;
							}
							.text {
								line-height: 1.5;
							}
						}
					}
					.right {
						display: flex;
						width: 66%;
						justify-content: space-between;
						flex-wrap: wrap;
						height: auto;
						.list-item  {
							border: 0px solid #efefef;
							cursor: pointer;
							padding: 5px 0;
							margin: 10px 0;
							background: #fff;
							display: flex;
							width: 48.5%;
							justify-content: space-between;
							height: auto;
							.date  {
								flex-direction: column;
								background: #f6f6f6;
								display: flex;
								width: 90px;
								border-color: #efefef;
								border-width: 0 0px 0 0;
								justify-content: center;
								align-items: center;
								border-style: solid;
								height: auto;
								.day  {
									color: #666;
									font-weight: 600;
									width: auto;
									font-size: 30px;
									border-color: #ddd;
									border-width: 0 0 2px;
									line-height: 40px;
									border-style: solid;
								}
								.year  {
									color: #999;
									width: auto;
									font-size: 14px;
									line-height: 40px;
								}
							}
							.info {
								flex: 1;
								.name  {
									padding: 0 10px;
									overflow: hidden;
									color: #333;
									white-space: nowrap;
									font-weight: 600;
									width: 100%;
									font-size: 14px;
									line-height: 1.5;
									text-overflow: ellipsis;
								}
								.desc  {
									padding: 0 10px;
									overflow: hidden;
									color: #666;
									font-size: 14px;
									line-height: 24px;
									height: 48px;
								}
								.publisher {
									padding: 0 10px;
									display: inline-block;
									.icon {
										margin: 0 2px 0 0;
										line-height: 1.5;
									}
									.label {
										line-height: 1.5;
									}
									.text {
										line-height: 1.5;
									}
								}
								.like {
									padding: 0 10px;
									display: inline-block;
									.icon {
										margin: 0 2px 0 0;
										line-height: 1.5;
									}
									.label {
										line-height: 1.5;
									}
									.text {
										line-height: 1.5;
									}
								}
								.collect {
									padding: 0 10px;
									display: inline-block;
									.icon {
										margin: 0 2px 0 0;
										line-height: 1.5;
									}
									.label {
										line-height: 1.5;
									}
									.text {
										line-height: 1.5;
									}
								}
								.view {
									padding: 0 10px;
									display: inline-block;
									.icon {
										margin: 0 2px 0 0;
										line-height: 1.5;
									}
									.label {
										line-height: 1.5;
									}
									.text {
										line-height: 1.5;
									}
								}
							}
						}
						.list-item:hover  {
							.date {
								.day {
									color: #333;
									border-color: #888;
								}
								.year {
									color: #333;
								}
							}
							.info {
								.name  {
									color: #000;
									font-weight: 600;
								}
								.desc  {
									color: #333;
								}
								.publisher {
									.icon {
										color: #333;
									}
									.label {
										color: #333;
									}
									.text {
										color: #333;
									}
								}
								.like {
									.icon {
										color: #333;
									}
									.label {
										color: #333;
									}
									.text {
										color: #333;
									}
								}
								.collect {
									.icon {
										color: #333;
									}
									.label {
										color: #333;
									}
									.text {
										color: #333;
									}
								}
								.view {
									.icon {
										color: #333;
									}
									.label {
										color: #333;
									}
									.text {
										color: #333;
									}
								}
							}
						}
					}
				}
			}
			.moreBtn {
				border: 0px solid #999;
				cursor: pointer;
				padding: 0;
				margin: 0;
				display: inline-block;
				line-height: 32px;
				right: calc((100% - 1200px)/2);
				float: right;
				top: 10px;
				background: none;
				width: auto;
				position: absolute;
				text-align: center;
				.text {
					color: #999;
					font-size: 15px;
				}
				.icon {
					color: #333;
					display: none;
					font-size: 15px;
				}
			}
		}
		.recommend {
			padding: 20px 0;
			margin: 0;
			background: url(http://codegen.caihongy.cn/20250208/b05d082118be46b9b378472f2e943282.jpg) fixed no-repeat center top / cover,#fff;
			width: 100%;
			position: relative;
			order: 3;
			.recommend_title_box {
				padding: 0 0 0 36px;
				margin: 0 auto 10px;
				background: url(http://codegen.caihongy.cn/20250207/abd42c84a0af467691b3957e9231cc32.png) no-repeat left center;
				width: 1200px;
				position: relative;
				text-align: left;
				.recommend_title {
					margin: 0;
					color: #fa710d;
					background: none;
					width: auto;
					font-size: 22px;
					line-height: 40px;
					text-align: left;
				}
				.recommend_subhead {
					margin: 0;
					color: #999;
					display: none;
					width: auto;
					font-size: 18px;
					line-height: 40px;
					text-align: center;
				}
			}
			.index-pv1 .animation-box {
				transform: rotate(0deg) scale(1) skew(0deg, 0deg) translate3d(0px, 0px, 0px);
				z-index: initial;
			}
			
			.index-pv1 .animation-box:hover {
				transform: rotate(0deg) scale(1) skew(0deg, 0deg) translate3d(0px, 0px, 0px);
				-webkit-perspective: 1000px;
				perspective: 1000px;
				transition: 0.3s;
				z-index: 1;
			}
			
			.index-pv1 .animation-box img {
				transform: rotate(0deg) scale(1) skew(0deg, 0deg) translate3d(0px, 0px, 0px);
			}
			
			.index-pv1 .animation-box img:hover {
				transform: rotate(0deg) scale(1) skew(0deg, 0deg) translate3d(0px, 0px, 0px);
				-webkit-perspective: 1000px;
				perspective: 1000px;
				transition: 0.3s;
			}
			.list16 {
				padding: 0;
				margin: 20px auto 0;
				color: #999;
				background: none;
				display: flex;
				width: 1200px;
				font-size: 14px;
				justify-content: space-between;
				flex-wrap: wrap;
				height: auto;
				.list-item {
					padding: 0;
					margin: 0 0 20px;
					background: #fff;
					width: 23%;
					position: relative;
					height: auto;
					.img {
						border: 0px solid #e8d1ad;
						padding: 10px;
						overflow: hidden;
						background: none;
						width: 100%;
						height: 200px;
						img {
							object-fit: cover;
							display: block;
							width: 100%;
							height: 100%;
						}
					}
					.infoBox {
						padding: 10px 10px;
						overflow: hidden;
						display: flex;
						width: 100%;
						height: auto;
						.info-left {
							padding: 0;
							width: 100%;
							line-height: 24px;
							.name {
								padding: 0 10px;
								overflow: hidden;
								color: #333;
								white-space: nowrap;
								font-weight: 600;
								width: 100%;
								font-size: 15px;
								line-height: 24px;
								text-overflow: ellipsis;
							}
							.price {
								padding: 0 10px;
								color: #f00;
								font-size: 16px;
								line-height: 2;
							}
							.time_item {
								padding: 0 10px;
								display: inline-block;
								.icon {
									margin: 0 2px 0 0;
									display: none;
								}
								.label {
								}
								.text {
								}
							}
							.publisher_item {
								padding: 0 10px;
								display: inline-block;
								.icon {
									margin: 0 2px 0 0;
									display: none;
								}
								.label {
								}
								.text {
								}
							}
							.like_item {
								padding: 0 10px;
								display: inline-block;
								.icon {
									margin: 0 2px 0 0;
									display: none;
								}
								.label {
								}
								.text {
								}
							}
							.collect_item {
								padding: 0 10px;
								display: inline-block;
								.icon {
									margin: 0 2px 0 0;
									display: none;
								}
								.label {
								}
								.text {
								}
							}
							.view_item {
								padding: 0 10px;
								display: inline-block;
								.icon {
									margin: 0 2px 0 0;
									display: none;
								}
								.label {
								}
								.text {
								}
							}
						}
						.desc {
							color: #666;
							flex: 1;
							display: none;
							font-size: 14px;
							line-height: 1.5;
						}
					}
				}
				.list-item:hover {
					cursor: pointer;
					background: #fce8d9;
					.infoBox {
						.info-left {
							.name {
							}
							.price {
							}
							.time_item {
								.icon {
								}
								.label {
								}
								.text {
								}
							}
							.publisher_item {
								.icon {
								}
								.label {
								}
								.text {
								}
							}
							.like_item {
								.icon {
									color: #fff;
								}
								.label {
								}
								.text {
								}
							}
							.collect_item {
								.icon {
								}
								.label {
								}
								.text {
								}
							}
							.view_item {
								.icon {
								}
								.label {
								}
								.text {
								}
							}
						}
						.desc {
							color: #fff;
						}
					}
				}
			}
			.moreBtn {
				border: 0px solid #999;
				cursor: pointer;
				padding: 0;
				margin: 0;
				display: inline-block;
				line-height: 32px;
				right: calc((100% - 1200px)/2);
				float: right;
				top: 20px;
				background: none;
				width: auto;
				position: absolute;
				text-align: center;
				.text {
					color: #999;
					font-size: 15px;
				}
				.icon {
					color: #333;
					display: none;
					font-size: 15px;
				}
			}
		}
		.lists {
			padding: 30px 0 20px;
			margin: 0;
			background: url() no-repeat center center;
			width: 100%;
			position: relative;
			order: 4;
			.list_title_box {
				padding: 0 0 0 36px;
				margin: 0 auto 10px;
				background: url(http://codegen.caihongy.cn/20250207/abd42c84a0af467691b3957e9231cc32.png) no-repeat left center;
				width: 1200px;
				position: relative;
				text-align: left;
				.list_title {
					margin: 0;
					color: #fa710d;
					background: none;
					width: auto;
					font-size: 22px;
					line-height: 40px;
					text-align: left;
				}
				.list_subhead {
					margin: 0;
					color: #999;
					display: none;
					width: auto;
					font-size: 18px;
					line-height: 40px;
					text-align: center;
				}
			}
			.index-pv1 .animation-box {
				transform: rotate(0deg) scale(1) skew(0deg, 0deg) translate3d(0px, 0px, 0px);
				z-index: initial;
			}
			
			.index-pv1 .animation-box:hover {
				transform: rotate(0deg) scale(1) skew(0deg, 0deg) translate3d(0px, 0px, 0px);
				-webkit-perspective: 1000px;
				perspective: 1000px;
				transition: 0.3s;
				z-index: 1;
			}
			
			.index-pv1 .animation-box img {
				transform: rotate(0deg) scale(1) skew(0deg, 0deg) translate3d(0px, 0px, 0px);
			}
			
			.index-pv1 .animation-box img:hover {
				transform: rotate(0deg) scale(1) skew(0deg, 0deg) translate3d(0px, 0px, 0px);
				-webkit-perspective: 1000px;
				perspective: 1000px;
				transition: 0.3s;
			}
			.list16 {
				padding: 0;
				margin: 10px auto 0;
				color: #999;
				background: none;
				display: flex;
				width: 1200px;
				font-size: 14px;
				justify-content: space-between;
				flex-wrap: wrap;
				height: auto;
				.list-item {
					cursor: pointer;
					padding: 0;
					margin: 0 0 0px;
					background: #fff;
					display: block;
					width: 23%;
					position: relative;
					height: auto;
					.img {
						border: 0px solid red;
						padding: 0;
						overflow: hidden;
						width: 100%;
						height: 200px;
						img {
							object-fit: cover;
							display: block;
							width: 100%;
							height: 100%;
						}
					}
					.infoBox {
						padding: 0 0 10px;
						overflow: hidden;
						display: flex;
						width: 100%;
						height: auto;
						.info-left {
							padding: 10px 0 0;
							flex: 1;
							width: 50%;
							line-height: 24px;
							.name {
								padding: 0 10px;
								overflow: hidden;
								color: #333;
								white-space: nowrap;
								font-weight: 600;
								width: 100%;
								font-size: 15px;
								line-height: 24px;
								text-overflow: ellipsis;
							}
							.price {
								padding: 5px 10px;
								color: #f00;
								font-size: 16px;
								line-height: 1.5;
							}
							.time_item {
								padding: 0 10px;
								display: inline-block;
								.icon {
									margin: 0 2px 0 0;
									display: none;
								}
								.label {
								}
								.text {
								}
							}
							.publisher_item {
								padding: 0 10px;
								display: inline-block;
								.icon {
									margin: 0 2px 0 0;
									display: none;
								}
								.label {
								}
								.text {
								}
							}
							.like_item {
								padding: 0 10px;
								display: inline-block;
								.icon {
									margin: 0 2px 0 0;
									display: none;
								}
								.label {
								}
								.text {
								}
							}
							.collect_item {
								padding: 0 10px;
								display: inline-block;
								.icon {
									margin: 0 2px 0 0;
									display: none;
								}
								.label {
								}
								.text {
								}
							}
							.view_item {
								padding: 0 10px;
								display: inline-block;
								.icon {
									margin: 0 2px 0 0;
									display: none;
								}
								.label {
								}
								.text {
								}
							}
						}
						.desc {
							color: #666;
							display: none;
							width: 50%;
							font-size: 14px;
							line-height: 1.5;
						}
					}
				}
				.list-item:hover {
					cursor: pointer;
					background: #fce8d9;
					.infoBox {
						.info-left {
							.name {
								color: #333;
							}
							.price {
								color: #f00;
							}
							.time_item {
								.icon {
									color: #333;
								}
								.label {
									color: #333;
								}
								.text {
									color: #333;
								}
							}
							.publisher_item {
								.icon {
									color: #333;
								}
								.label {
									color: #333;
								}
								.text {
									color: #333;
								}
							}
							.like_item {
								.icon {
									color: #333;
								}
								.label {
									color: #333;
								}
								.text {
									color: #333;
								}
							}
							.collect_item {
								.icon {
									color: #333;
								}
								.label {
									color: #333;
								}
								.text {
									color: #333;
								}
							}
							.view_item {
								.icon {
									color: #333;
								}
								.label {
									color: #333;
								}
								.text {
									color: #333;
								}
							}
						}
						.desc {
							color: #fff;
						}
					}
				}
			}
			.moreBtn {
				border: 0px solid #999;
				cursor: pointer;
				padding: 0;
				margin: 0;
				display: inline-block;
				line-height: 32px;
				right: calc((100% - 1200px)/2);
				float: right;
				top: 20px;
				background: none;
				width: auto;
				position: absolute;
				text-align: center;
				.text {
					color: #999;
					font-size: 15px;
				}
				.icon {
					color: #333;
					display: none;
					font-size: 15px;
				}
			}
		}
	}
</style>
