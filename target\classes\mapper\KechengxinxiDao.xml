<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.dao.KechengxinxiDao">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.entity.KechengxinxiEntity" id="kechengxinxiMap">
        <result property="kechengmingcheng" column="kechengmingcheng"/>
        <result property="dagang" column="dagang"/>
        <result property="kechengleixing" column="kechengleixing"/>
        <result property="fengmian" column="fengmian"/>
        <result property="kejian" column="kejian"/>
        <result property="jiaoxueshipin" column="jiaoxueshipin"/>
        <result property="kechengxiangqing" column="kechengxiangqing"/>
        <result property="fabushijian" column="fabushijian"/>
        <result property="jiaoshigonghao" column="jiaoshigonghao"/>
        <result property="jiaoshixingming" column="jiaoshixingming"/>
        <result property="sfsh" column="sfsh"/>
        <result property="shhf" column="shhf"/>
        <result property="clicktime" column="clicktime"/>
        <result property="clicknum" column="clicknum"/>
        <result property="discussnum" column="discussnum"/>
        <result property="totalscore" column="totalscore"/>
        <result property="storeupnum" column="storeupnum"/>
    </resultMap>

	<select id="selectListVO"
		resultType="com.entity.vo.KechengxinxiVO" >
		SELECT * FROM kechengxinxi  kechengxinxi
        <where> 1=1 ${ew.sqlSegment}</where>
	</select>

	<select id="selectVO"
		resultType="com.entity.vo.KechengxinxiVO" >
		SELECT  kechengxinxi.* FROM kechengxinxi  kechengxinxi
 		<where> 1=1 ${ew.sqlSegment}</where>
	</select>

    <select id="selectListView"
		resultType="com.entity.view.KechengxinxiView" >

		SELECT  kechengxinxi.* FROM kechengxinxi  kechengxinxi
        <where> 1=1 ${ew.sqlSegment}</where>
	</select>

	<select id="selectView"
		resultType="com.entity.view.KechengxinxiView" >
		SELECT * FROM kechengxinxi  kechengxinxi <where> 1=1 ${ew.sqlSegment}</where>
	</select>


    <select id="selectValue" resultType="map" >
        SELECT ${params.xColumn}, ROUND(sum(${params.yColumn}),1) total FROM kechengxinxi
        <where> 1=1 ${ew.sqlSegment}</where>
        group by ${params.xColumn}
    </select>

    <select id="selectTimeStatValue" resultType="map" >
        <if test = 'params.timeStatType == "日"'>
                SELECT DATE_FORMAT(${params.xColumn},'%Y-%m-%d') ${params.xColumn}, sum(${params.yColumn}) total FROM kechengxinxi
                <where> 1=1 ${ew.sqlSegment}</where>
                group by DATE_FORMAT(${params.xColumn},'%Y-%m-%d')
        </if>
        <if test = 'params.timeStatType == "月"'>
                SELECT DATE_FORMAT(${params.xColumn},'%Y-%m') ${params.xColumn}, sum(${params.yColumn}) total FROM kechengxinxi
                <where> 1=1 ${ew.sqlSegment}</where>
                group by DATE_FORMAT(${params.xColumn},'%Y-%m')
        </if>
        <if test='params.timeStatType == "季"'>
                SELECT CONCAT(YEAR(${params.xColumn}), ' - ', ELT(QUARTER(${params.xColumn}), '第一季度', '第二季度', '第三季度', '第四季度')) as ${params.xColumn}, sum(${params.yColumn}) total FROM kechengxinxi
                <where> 1=1 ${ew.sqlSegment}</where>
                group by CONCAT(YEAR(${params.xColumn}), ' - ', ELT(QUARTER(${params.xColumn}), '第一季度', '第二季度', '第三季度', '第四季度'))
        </if>
        <if test = 'params.timeStatType == "年"'>
                SELECT DATE_FORMAT(${params.xColumn},'%Y') ${params.xColumn}, sum(${params.yColumn}) total FROM kechengxinxi
                <where> 1=1 ${ew.sqlSegment}</where>
                group by DATE_FORMAT(${params.xColumn},'%Y')
        </if>
    </select>

    <!-- 分组查询 -->
    <select id="selectGroup" resultType="map">
        SELECT
            ${column} as ${column},
            COUNT(1) as total
        FROM
            kechengxinxi
        WHERE
            1=1
        GROUP BY
            ${column}
    </select>





</mapper>
