<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.dao.XiangmuleixingDao">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.entity.XiangmuleixingEntity" id="xiangmuleixingMap">
        <result property="xiangmuleixing" column="xiangmuleixing"/>
    </resultMap>

	<select id="selectListVO"
		resultType="com.entity.vo.XiangmuleixingVO" >
		SELECT * FROM xiangmuleixing  xiangmuleixing         
        <where> 1=1 ${ew.sqlSegment}</where>
	</select>
	
	<select id="selectVO"
		resultType="com.entity.vo.XiangmuleixingVO" >
		SELECT  xiangmuleixing.* FROM xiangmuleixing  xiangmuleixing 	
 		<where> 1=1 ${ew.sqlSegment}</where>
	</select>

    <select id="selectListView"
		resultType="com.entity.view.XiangmuleixingView" >

		SELECT  xiangmuleixing.* FROM xiangmuleixing  xiangmuleixing 	        
        <where> 1=1 ${ew.sqlSegment}</where>
	</select>
	
	<select id="selectView"
		resultType="com.entity.view.XiangmuleixingView" >
		SELECT * FROM xiangmuleixing  xiangmuleixing <where> 1=1 ${ew.sqlSegment}</where>
	</select>
	


</mapper>
