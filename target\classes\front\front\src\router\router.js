import VueRouter from 'vue-router'
//引入组件
import Index from '../pages'
import Home from '../pages/home/<USER>'
import Login from '../pages/login/login'
import Register from '../pages/register/register'
import Center from '../pages/center/center'
import Forum from '../pages/forum/list'
import ForumAdd from '../pages/forum/add'
import ForumDetail from '../pages/forum/detail'
import MyForumList from '../pages/forum/myForumList'
import ExamPaper from '../pages/exam/examPaper'
import Exam from '../pages/exam/exam'
import ExamList from '../pages/exam/examList'
import ExamRecord from '../pages/exam/examRecord'
import Storeup from '../pages/storeup/list'
import News from '../pages/news/news-list'
import NewsDetail from '../pages/news/news-detail'
import payList from '../pages/pay'

import jiaoshiList from '../pages/jiaoshi/list'
import jiaoshiDetail from '../pages/jiaoshi/detail'
import jiaoshiAdd from '../pages/jiaoshi/add'
import xueshengList from '../pages/xuesheng/list'
import xueshengDetail from '../pages/xuesheng/detail'
import xueshengAdd from '../pages/xuesheng/add'
import banjiList from '../pages/banji/list'
import banjiDetail from '../pages/banji/detail'
import banjiAdd from '../pages/banji/add'
import ziliaofenleiList from '../pages/ziliaofenlei/list'
import ziliaofenleiDetail from '../pages/ziliaofenlei/detail'
import ziliaofenleiAdd from '../pages/ziliaofenlei/add'
import xuexiziliaoList from '../pages/xuexiziliao/list'
import xuexiziliaoDetail from '../pages/xuexiziliao/detail'
import xuexiziliaoAdd from '../pages/xuexiziliao/add'
import kechengleixingList from '../pages/kechengleixing/list'
import kechengleixingDetail from '../pages/kechengleixing/detail'
import kechengleixingAdd from '../pages/kechengleixing/add'
import kechengxinxiList from '../pages/kechengxinxi/list'
import kechengxinxiDetail from '../pages/kechengxinxi/detail'
import kechengxinxiAdd from '../pages/kechengxinxi/add'
import xiangmuleixingList from '../pages/xiangmuleixing/list'
import xiangmuleixingDetail from '../pages/xiangmuleixing/detail'
import xiangmuleixingAdd from '../pages/xiangmuleixing/add'
import zuoyexinxiList from '../pages/zuoyexinxi/list'
import zuoyexinxiDetail from '../pages/zuoyexinxi/detail'
import zuoyexinxiAdd from '../pages/zuoyexinxi/add'
import tijiaozuoyeList from '../pages/tijiaozuoye/list'
import tijiaozuoyeDetail from '../pages/tijiaozuoye/detail'
import tijiaozuoyeAdd from '../pages/tijiaozuoye/add'
import zuoyechengjiList from '../pages/zuoyechengji/list'
import zuoyechengjiDetail from '../pages/zuoyechengji/detail'
import zuoyechengjiAdd from '../pages/zuoyechengji/add'
import forumtypeList from '../pages/forumtype/list'
import forumtypeDetail from '../pages/forumtype/detail'
import forumtypeAdd from '../pages/forumtype/add'
import forumreportList from '../pages/forumreport/list'
import forumreportDetail from '../pages/forumreport/detail'
import forumreportAdd from '../pages/forumreport/add'
import newstypeList from '../pages/newstype/list'
import newstypeDetail from '../pages/newstype/detail'
import newstypeAdd from '../pages/newstype/add'
import discussxuexiziliaoList from '../pages/discussxuexiziliao/list'
import discussxuexiziliaoDetail from '../pages/discussxuexiziliao/detail'
import discussxuexiziliaoAdd from '../pages/discussxuexiziliao/add'
import discusskechengxinxiList from '../pages/discusskechengxinxi/list'
import discusskechengxinxiDetail from '../pages/discusskechengxinxi/detail'
import discusskechengxinxiAdd from '../pages/discusskechengxinxi/add'

const originalPush = VueRouter.prototype.push
VueRouter.prototype.push = function push(location) {
	return originalPush.call(this, location).catch(err => err)
}

//配置路由
export default new VueRouter({
	routes:[
		{
      path: '/',
      redirect: '/index/home'
    },
		{
			path: '/index',
			component: Index,
			children:[
				{
					path: 'home',
					component: Home
				},
				{
					path: 'center',
					component: Center,
				},
				{
					path: 'pay',
					component: payList,
				},
				{
					path: 'forum',
					component: Forum
				},
				{
					path: 'forumAdd',
					component: ForumAdd
				},
				{
					path: 'forumDetail',
					component: ForumDetail
				},
				{
					path: 'myForumList',
					component: MyForumList
				},
				{
					path: 'examPaper',
					component: ExamPaper
				},
				{
					path: 'examList',
					component:ExamList
				},
				{
					path: 'examRecord/:type',
					component:ExamRecord
				},
				{
					path: 'storeup',
					component: Storeup
				},
				{
					path: 'news',
					component: News
				},
				{
					path: 'newsDetail',
					component: NewsDetail
				},
				{
					path: 'jiaoshi',
					component: jiaoshiList
				},
				{
					path: 'jiaoshiDetail',
					component: jiaoshiDetail
				},
				{
					path: 'jiaoshiAdd',
					component: jiaoshiAdd
				},
				{
					path: 'xuesheng',
					component: xueshengList
				},
				{
					path: 'xueshengDetail',
					component: xueshengDetail
				},
				{
					path: 'xueshengAdd',
					component: xueshengAdd
				},
				{
					path: 'banji',
					component: banjiList
				},
				{
					path: 'banjiDetail',
					component: banjiDetail
				},
				{
					path: 'banjiAdd',
					component: banjiAdd
				},
				{
					path: 'ziliaofenlei',
					component: ziliaofenleiList
				},
				{
					path: 'ziliaofenleiDetail',
					component: ziliaofenleiDetail
				},
				{
					path: 'ziliaofenleiAdd',
					component: ziliaofenleiAdd
				},
				{
					path: 'xuexiziliao',
					component: xuexiziliaoList
				},
				{
					path: 'xuexiziliaoDetail',
					component: xuexiziliaoDetail
				},
				{
					path: 'xuexiziliaoAdd',
					component: xuexiziliaoAdd
				},
				{
					path: 'kechengleixing',
					component: kechengleixingList
				},
				{
					path: 'kechengleixingDetail',
					component: kechengleixingDetail
				},
				{
					path: 'kechengleixingAdd',
					component: kechengleixingAdd
				},
				{
					path: 'kechengxinxi',
					component: kechengxinxiList
				},
				{
					path: 'kechengxinxiDetail',
					component: kechengxinxiDetail
				},
				{
					path: 'kechengxinxiAdd',
					component: kechengxinxiAdd
				},
				{
					path: 'xiangmuleixing',
					component: xiangmuleixingList
				},
				{
					path: 'xiangmuleixingDetail',
					component: xiangmuleixingDetail
				},
				{
					path: 'xiangmuleixingAdd',
					component: xiangmuleixingAdd
				},
				
				{
					path: 'zuoyexinxi',
					component: zuoyexinxiList
				},
				{
					path: 'zuoyexinxiDetail',
					component: zuoyexinxiDetail
				},
				{
					path: 'zuoyexinxiAdd',
					component: zuoyexinxiAdd
				},
				{
					path: 'tijiaozuoye',
					component: tijiaozuoyeList
				},
				{
					path: 'tijiaozuoyeDetail',
					component: tijiaozuoyeDetail
				},
				{
					path: 'tijiaozuoyeAdd',
					component: tijiaozuoyeAdd
				},
				{
					path: 'zuoyechengji',
					component: zuoyechengjiList
				},
				{
					path: 'zuoyechengjiDetail',
					component: zuoyechengjiDetail
				},
				{
					path: 'zuoyechengjiAdd',
					component: zuoyechengjiAdd
				},
				{
					path: 'forumtype',
					component: forumtypeList
				},
				{
					path: 'forumtypeDetail',
					component: forumtypeDetail
				},
				{
					path: 'forumtypeAdd',
					component: forumtypeAdd
				},
				{
					path: 'forumreport',
					component: forumreportList
				},
				{
					path: 'forumreportDetail',
					component: forumreportDetail
				},
				{
					path: 'forumreportAdd',
					component: forumreportAdd
				},
				{
					path: 'newstype',
					component: newstypeList
				},
				{
					path: 'newstypeDetail',
					component: newstypeDetail
				},
				{
					path: 'newstypeAdd',
					component: newstypeAdd
				},
				{
					path: 'discussxuexiziliao',
					component: discussxuexiziliaoList
				},
				{
					path: 'discussxuexiziliaoDetail',
					component: discussxuexiziliaoDetail
				},
				{
					path: 'discussxuexiziliaoAdd',
					component: discussxuexiziliaoAdd
				},
				{
					path: 'discusskechengxinxi',
					component: discusskechengxinxiList
				},
				{
					path: 'discusskechengxinxiDetail',
					component: discusskechengxinxiDetail
				},
				{
					path: 'discusskechengxinxiAdd',
					component: discusskechengxinxiAdd
				},
			]
		},
		{
			path: '/login',
			component: Login
		},
		{
			path: '/register',
			component: Register
		},
		{
			path: '/exam',
			component: Exam
		}
	]
})
